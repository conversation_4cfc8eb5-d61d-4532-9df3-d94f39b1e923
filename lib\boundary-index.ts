/**
 * BoundaryIndex - 高效的行位置索引系统
 * 用于快速计算文档中任意位置的行列坐标与字符偏移量之间的转换
 */

export interface Position {
  line: number;
  col: number;
}

export interface ChangeDesc {
  iterChanges(f: (fromA: number, toA: number, fromB: number, toB: number, inserted: any) => void): void;
}

export class BoundaryIndex {
  private lineStart: Uint32Array;
  private content: string;

  constructor(content: string) {
    this.content = content;
    this.lineStart = this.buildLineStartArray(content);
  }

  /**
   * 构建行首偏移量数组
   * lineStart[i] 表示第i行的起始字符偏移量
   */
  private buildLineStartArray(content: string): Uint32Array {
    const lines = content.split('\n');
    const lineStart = new Uint32Array(lines.length + 1);
    let offset = 0;

    for (let i = 0; i < lines.length; i++) {
      lineStart[i] = offset;
      offset += lines[i].length + 1; // +1 for '\n'
    }
    
    // 最后一个位置：如果文档末尾没有换行符，不加1
    lineStart[lines.length] = content.endsWith('\n') ? offset : offset - 1;
    
    return lineStart;
  }

  /**
   * 将行列位置转换为字符偏移量 - O(1)
   * 
   * @param pos 行列位置
   * @returns 字符偏移量
   */
  toOffset(pos: Position): number {
    if (pos.line >= this.lineStart.length - 1) {
      return this.content.length;
    }
    return this.lineStart[pos.line] + pos.col;
  }

  /**
   * 将字符偏移量转换为行列位置 - O(log N)
   * 使用二分查找
   * 
   * @param offset 字符偏移量
   * @returns 行列位置
   */
  toPos(offset: number): Position {
    if (offset <= 0) return { line: 0, col: 0 };
    if (offset >= this.content.length) {
      const lastLine = this.lineStart.length - 2;
      const lastLineStart = this.lineStart[lastLine];
      return {
        line: lastLine,
        col: this.content.length - lastLineStart
      };
    }

    // 二分查找
    let left = 0;
    let right = this.lineStart.length - 1;
    
    while (left < right) {
      const mid = Math.floor((left + right + 1) / 2);
      if (this.lineStart[mid] <= offset) {
        left = mid;
      } else {
        right = mid - 1;
      }
    }

    return {
      line: left,
      col: offset - this.lineStart[left]
    };
  }

  /**
   * 获取指定行的起始偏移量
   * 
   * @param line 行号（0-based）
   * @returns 起始偏移量
   */
  getLineStart(line: number): number {
    if (line >= this.lineStart.length - 1) {
      return this.content.length;
    }
    return this.lineStart[line];
  }

  /**
   * 获取文档总行数
   */
  getLineCount(): number {
    return this.lineStart.length - 1;
  }

  /**
   * 应用变更并增量更新索引
   * 注意：这是M2阶段的功能，M1阶段暂时重建整个索引
   * 
   * @param changes CodeMirror的变更描述
   * @param newContent 新的文档内容
   */
  applyChanges(changes: ChangeDesc, newContent: string): void {
    // M1阶段简化实现：完全重建索引
    // M2阶段将实现真正的增量更新
    this.content = newContent;
    this.lineStart = this.buildLineStartArray(newContent);
  }

  /**
   * 计算指定范围内的行数
   * 
   * @param startOffset 起始偏移量
   * @param endOffset 结束偏移量
   * @returns 行数
   */
  getLineCountInRange(startOffset: number, endOffset: number): number {
    const startPos = this.toPos(startOffset);
    const endPos = this.toPos(endOffset);
    return endPos.line - startPos.line + 1;
  }

<<<<<<< Updated upstream
  /**
   * 获取当前内容的副本（只读）
   */
  getContent(): string {
    return this.content;
=======
  return segments;
};

/**
 * 防抖旁路判断 - 大块变更绕过防抖
 */
export const shouldBypassDebounce = (changeInfo: {
  lines?: number;
  bytes?: number;
}): boolean => {
  const LARGE_CHANGE_THRESHOLD = {
    lines: 200,
    bytes: 64 * 1024 // 64KB
  };

  return (changeInfo.lines && changeInfo.lines >= LARGE_CHANGE_THRESHOLD.lines) ||
         (changeInfo.bytes && changeInfo.bytes >= LARGE_CHANGE_THRESHOLD.bytes);
};

/**
 * 计算自适应防抖时间 - 根据文档复杂度动态调整
 * 公式：clamp(80 + 0.004 * totalLines, 80, 200) ms
 */
export const calculateAdaptiveDebounce = (totalLines: number): number => {
  return Math.max(80, Math.min(200, 80 + 0.004 * totalLines));
};

/**
 * 立即刷新条件判断 - 输入停止超过300ms立即刷新
 */
export const shouldImmediateRefresh = (inputIdleTime: number): boolean => {
  return inputIdleTime >= 300; // 300ms
};

/**
 * 语义锚点滚动相关功能
 */

// 标题信息接口
export interface HeadingInfo {
  id: string;
  text: string;
  level: number;
  element?: HTMLElement;
  offsetTop: number;
}

// 滚动位置信息
export interface ScrollPosition {
  headingId?: string;
  headingOffset?: number; // 相对于标题的偏移百分比
  absoluteOffset?: number; // 绝对偏移位置
}

/**
 * 提取文档中的所有标题信息
 */
export const extractHeadings = (container: HTMLElement): HeadingInfo[] => {
  const headings: HeadingInfo[] = [];
  const headingElements = container.querySelectorAll('h1, h2, h3, h4, h5, h6');

  headingElements.forEach((element) => {
    const htmlElement = element as HTMLElement;
    const level = parseInt(htmlElement.tagName.charAt(1));
    const text = htmlElement.textContent || '';
    const id = htmlElement.id || generateHeadingId(text);

    // 确保元素有ID
    if (!htmlElement.id) {
      htmlElement.id = id;
    }

    headings.push({
      id,
      text,
      level,
      element: htmlElement,
      offsetTop: htmlElement.offsetTop
    });
  });

  return headings;
};

/**
 * 生成标题ID
 */
const generateHeadingId = (text: string): string => {
  return text.toLowerCase()
    .replace(/[^\w\u4e00-\u9fa5\s-]/g, '')  // 保留中英文字符
    .replace(/\s+/g, '-')                    // 空格转连字符
    .replace(/-+/g, '-')                     // 多个连字符合并
    .replace(/^-|-$/g, '');                  // 移除首尾连字符
};

/**
 * 计算当前滚动位置的语义锚点
 */
export const calculateSemanticAnchor = (
  container: HTMLElement,
  scrollTop: number
): ScrollPosition => {
  const headings = extractHeadings(container);

  if (headings.length === 0) {
    return { absoluteOffset: scrollTop };
  }

  // 找到最近的标题
  let nearestHeading: HeadingInfo | null = null;

  for (let i = headings.length - 1; i >= 0; i--) {
    if (headings[i].offsetTop <= scrollTop + 100) { // 100px容错
      nearestHeading = headings[i];
      break;
    }
  }

  if (!nearestHeading) {
    // 如果没有找到合适的标题，使用第一个标题
    nearestHeading = headings[0];
  }

  // 计算相对于标题的偏移百分比
  const nextHeading = headings.find(h => h.offsetTop > nearestHeading!.offsetTop);
  const sectionHeight = nextHeading
    ? nextHeading.offsetTop - nearestHeading.offsetTop
    : container.scrollHeight - nearestHeading.offsetTop;

  const relativeOffset = scrollTop - nearestHeading.offsetTop;
  const offsetPercentage = Math.max(0, Math.min(1, relativeOffset / sectionHeight));

  return {
    headingId: nearestHeading.id,
    headingOffset: offsetPercentage,
    absoluteOffset: scrollTop
  };
};

/**
 * 根据语义锚点恢复滚动位置
 */
export const restoreSemanticScroll = (
  container: HTMLElement,
  position: ScrollPosition
): boolean => {
  if (position.headingId) {
    const headingElement = container.querySelector(`#${position.headingId}`) as HTMLElement;

    if (headingElement) {
      const headings = extractHeadings(container);
      const currentHeading = headings.find(h => h.id === position.headingId);

      if (currentHeading) {
        // 计算目标滚动位置
        const nextHeading = headings.find(h => h.offsetTop > currentHeading.offsetTop);
        const sectionHeight = nextHeading
          ? nextHeading.offsetTop - currentHeading.offsetTop
          : container.scrollHeight - currentHeading.offsetTop;

        const targetOffset = currentHeading.offsetTop +
          (sectionHeight * (position.headingOffset || 0));

        container.scrollTop = targetOffset;
        return true;
      }
    }
  }

  // 降级到绝对位置
  if (position.absoluteOffset !== undefined) {
    container.scrollTop = position.absoluteOffset;
    return true;
  }

  return false;
};

/**
 * 局部重分段核心钩子函数
 */

// 计算受影响的行范围
export const calculateAffectedLineRange = (
  changes: any,
  boundaryIndex: BoundaryIndex
): { start: number; end: number } => {
  let minLine = Infinity;
  let maxLine = -1;

  changes.iterChanges((fromA: number, toA: number) => {
    const startPos = boundaryIndex.toPos(fromA);
    const endPos = boundaryIndex.toPos(toA);

    minLine = Math.min(minLine, startPos.line);
    maxLine = Math.max(maxLine, endPos.line);
  });

  // 外扩1行作为缓冲，处理边界情况
  return {
    start: Math.max(0, minLine - 1),
    end: Math.min(boundaryIndex.getLineCount() - 1, maxLine + 1)
  };
};

// 找到受影响的段落索引范围
export const findAffectedSegments = (
  segments: ContentSegment[],
  lineRange: { start: number; end: number },
  boundaryIndex: BoundaryIndex
): { start: number; end: number } => {
  const startOffset = boundaryIndex.getLineStart(lineRange.start);
  const endOffset = boundaryIndex.getLineStart(lineRange.end);

  let startIndex = -1;
  let endIndex = -1;

  // 二分查找受影响的段落范围
  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i];

    // 段落与受影响范围有交集
    if (segment.startOffset <= endOffset &&
        segment.startOffset + segment.content.length >= startOffset) {
      if (startIndex === -1) startIndex = i;
      endIndex = i;
    }
>>>>>>> Stashed changes
  }

  /**
   * 调试用：打印索引信息
   */
  debug(): void {
    console.log('BoundaryIndex Debug Info:');
    console.log(`Content length: ${this.content.length}`);
    console.log(`Line count: ${this.getLineCount()}`);
    console.log('Line starts:', Array.from(this.lineStart).slice(0, 10));
  }
} 