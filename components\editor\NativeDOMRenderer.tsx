'use client';

import React, { useRef, useEffect, useCallback } from 'react';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeHighlight from 'rehype-highlight';
import rehypeStringify from 'rehype-stringify';
import rehypeRaw from 'rehype-raw';
import { BoundaryIndex } from '@/lib/boundary-index';
import { generateStableSegmentId, fastHash32 } from '@/lib/hash-utils';
import { secureRenderSegment } from '@/lib/secure-renderer';
import { performanceMonitor } from '@/lib/performance-monitor';

interface NativeDOMRendererProps {
  content: string;
  className?: string;
  initialScrollPosition?: number;
  onScrollPositionChange?: (position: number) => void;
}

interface ContentSegment {
  id: string;
  content: string;
  hash: string; // 内容哈希，用于检测变化
  startOffset: number; // 段落在文档中的起始字符偏移量
  endOffset: number; // 段落在文档中的结束字符偏移量
  element?: HTMLElement;
}

// 使用新的快速哈希函数（已移至hash-utils.ts）
// const hashString = fastHash32; // 兼容性别名

// M1: 使用安全的段落渲染器
const renderSegment = secureRenderSegment;

// 分割内容为段落 - 使用稳定的段落ID
const splitContent = (content: string): ContentSegment[] => {
  if (!content.trim()) return [];

  const lines = content.split('\n');
  const segments: ContentSegment[] = [];
  const boundaryIndex = new BoundaryIndex(content);

  let currentSegment = '';
  let segmentStartLine = 0;
  let inCodeBlock = false;

  const createSegment = (segmentContent: string, startLine: number, endLine: number): ContentSegment => {
    const trimmedContent = segmentContent.trim();
    const startOffset = boundaryIndex.getLineStart(startLine);
    const endOffset = boundaryIndex.getLineStart(endLine + 1) - 1; // 减1避免包含下一行的开始
    
    return {
      id: generateStableSegmentId(trimmedContent, startOffset),
      content: trimmedContent,
      hash: fastHash32(trimmedContent),
      startOffset,
      endOffset: Math.min(endOffset, content.length - 1)
    };
  };

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 检测代码块边界
    if (line.trim().startsWith('```')) {
      inCodeBlock = !inCodeBlock;
      currentSegment += (currentSegment ? '\n' : '') + line;
      
      // 代码块结束时创建段落
      if (!inCodeBlock && currentSegment.trim()) {
        segments.push(createSegment(currentSegment, segmentStartLine, i));
        currentSegment = '';
        segmentStartLine = i + 1;
      }
      continue;
    }

    // 在代码块内，直接添加行
    if (inCodeBlock) {
      currentSegment += (currentSegment ? '\n' : '') + line;
      continue;
    }

    // 检测段落边界（空行或标题）
    const isEmptyLine = line.trim() === '';
    const isHeading = line.trim().match(/^#{1,6}\s/);

    if (isEmptyLine || isHeading) {
      // 保存当前段落
      if (currentSegment.trim()) {
        segments.push(createSegment(currentSegment, segmentStartLine, i - 1));
      }

      // 如果是标题，开始新段落
      if (isHeading) {
        currentSegment = line;
        segmentStartLine = i;
      } else {
        currentSegment = '';
        segmentStartLine = i + 1;
      }
    } else {
      // 添加到当前段落
      currentSegment += (currentSegment ? '\n' : '') + line;
    }
  }

  // 处理最后一个段落
  if (currentSegment.trim()) {
    segments.push(createSegment(currentSegment, segmentStartLine, lines.length - 1));
  }

  return segments;
};

// 渲染单个段落
const renderSegmentContent = (content: string): string => {
  try {
    const processor = unified()
      .use(remarkParse)
      .use(remarkGfm)
      .use(remarkRehype, { allowDangerousHtml: true })
      .use(rehypeRaw)
      .use(rehypeHighlight)
      .use(rehypeStringify);

    const result = processor.processSync(content);
    return String(result);
  } catch (error) {
    console.error('Segment rendering error:', error);
    return `<p>渲染错误: ${error}</p>`;
  }
};

/**
 * keyed-diff DOM更新算法 - 支持add/remove/update/move操作
 * 避免innerHTML清空，实现真正的增量更新
 */
const updateDOMKeyed = (
  container: HTMLElement,
  oldSegments: ContentSegment[],
  newSegments: ContentSegment[]
) => {
  // 构建映射表
  const oldMap = new Map(oldSegments.map(seg => [seg.id, seg]));
  const newMap = new Map(newSegments.map(seg => [seg.id, seg]));

  // 获取现有DOM元素映射
  const existingElements = Array.from(container.children) as HTMLElement[];
  const elementMap = new Map<string, HTMLElement>();

  existingElements.forEach(el => {
    const segmentId = el.getAttribute('data-segment-id');
    if (segmentId) {
      elementMap.set(segmentId, el);
    }
  });

  // 计算需要的操作：add/remove/update/move
  const operations: Array<{
    type: 'add' | 'remove' | 'update' | 'move';
    segmentId: string;
    element?: HTMLElement;
    newIndex?: number;
    content?: string;
  }> = [];

  // 1. 标记需要删除的元素
  oldSegments.forEach(oldSeg => {
    if (!newMap.has(oldSeg.id)) {
      operations.push({
        type: 'remove',
        segmentId: oldSeg.id,
        element: elementMap.get(oldSeg.id)
      });
    }
  });

  // 2. 处理新增、更新和移动
  newSegments.forEach((newSeg, index) => {
    const oldSeg = oldMap.get(newSeg.id);
    const existingElement = elementMap.get(newSeg.id);

    if (!oldSeg) {
      // 新增段落
      operations.push({
        type: 'add',
        segmentId: newSeg.id,
        newIndex: index,
        content: newSeg.content
      });
    } else if (oldSeg.hash !== newSeg.hash) {
      // 内容变化，需要更新
      operations.push({
        type: 'update',
        segmentId: newSeg.id,
        element: existingElement,
        content: newSeg.content
      });
    }

    // 检查位置是否需要调整（移动操作）
    if (existingElement) {
      const currentIndex = Array.from(container.children).indexOf(existingElement);
      if (currentIndex !== index && currentIndex !== -1) {
        operations.push({
          type: 'move',
          segmentId: newSeg.id,
          element: existingElement,
          newIndex: index
        });
      }
    }
  });

  // 3. 执行DOM操作 - 按类型分组执行，避免索引混乱

  // 先执行删除操作
  operations.filter(op => op.type === 'remove').forEach(op => {
    if (op.element && op.element.parentNode === container) {
      container.removeChild(op.element);
    }
  });

  // 再执行更新操作
  operations.filter(op => op.type === 'update').forEach(op => {
    if (op.element && op.content) {
      const html = renderSegmentContent(op.content);
      op.element.innerHTML = html;
    }
  });

  // 最后执行添加和移动操作，使用DocumentFragment批量处理
  const fragment = document.createDocumentFragment();
  const elementsToPlace: Array<{ element: HTMLElement; index: number }> = [];

  // 收集所有需要放置的元素
  newSegments.forEach((segment, index) => {
    let element = elementMap.get(segment.id);

    if (!element) {
      // 创建新元素
      element = document.createElement('div');
      element.className = 'markdown-segment';
      element.setAttribute('data-segment-id', segment.id);

      const html = renderSegmentContent(segment.content);
      element.innerHTML = html;
    }

    elementsToPlace.push({ element, index });
  });

  // 一次性重新排列所有元素
  elementsToPlace
    .sort((a, b) => a.index - b.index)
    .forEach(({ element }) => {
      fragment.appendChild(element);
    });

  // 一次性提交到DOM
  container.replaceChildren(fragment);
};

export default function NativeDOMRenderer({
  content,
  className = '',
  initialScrollPosition,
  onScrollPositionChange
}: NativeDOMRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const segmentsRef = useRef<ContentSegment[]>([]);
  const scrollRestoredRef = useRef(false);

  // M1: 实现keyed-diff算法 - 精确增量DOM更新
  const updateDOMKeyed = useCallback((newSegments: ContentSegment[]) => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const oldSegments = segmentsRef.current;
    
    // 构建映射
    const oldMap = new Map(oldSegments.map((seg) => [seg.id, seg]));
    const newMap = new Map(newSegments.map((seg) => [seg.id, seg]));
    
    // 获取现有DOM元素映射
    const existingElements = Array.from(container.children) as HTMLElement[];
    const elementMap = new Map<string, HTMLElement>();
    
    existingElements.forEach(el => {
      const segmentId = el.getAttribute('data-segment-id');
      if (segmentId) {
        elementMap.set(segmentId, el);
      }
    });

    // 使用keyed-diff算法进行增量更新
    updateDOMKeyed(container, oldSegments, newSegments);

    // 更新引用
    segmentsRef.current = newSegments;


  }, []);

  // 监听内容变化 - 使用新的keyed-diff算法 + 性能监控
  useEffect(() => {
    performanceMonitor.markStart('content-parsing');
    const newSegments = splitContent(content);
    const parseTime = performanceMonitor.markEnd('content-parsing');
    
    performanceMonitor.markStart('dom-update');
    updateDOMKeyed(newSegments);
    const updateTime = performanceMonitor.markEnd('dom-update');
    
    // 记录输入延迟（解析+渲染总时间）
    performanceMonitor.recordInputLatency(parseTime + updateTime);
    
    // 在开发环境输出性能信息
    if (process.env.NODE_ENV === 'development' && (parseTime + updateTime) > 50) {
      console.log(`📊 渲染性能: 解析${parseTime.toFixed(2)}ms + 更新${updateTime.toFixed(2)}ms = ${(parseTime + updateTime).toFixed(2)}ms`);
    }
  }, [content, updateDOMKeyed]);

  // M1: 改进的滚动位置恢复 - 避免强制设置
  useEffect(() => {
    if (containerRef.current && 
        initialScrollPosition !== undefined && 
        !scrollRestoredRef.current) {
      
      requestAnimationFrame(() => {
        if (containerRef.current) {
          // 使用scrollTo替代直接设置scrollTop，提供更平滑的体验
          containerRef.current.scrollTo({
            top: initialScrollPosition,
            behavior: 'auto' // M2阶段将改为语义锚点定位
          });
          scrollRestoredRef.current = true;
        }
      });
    }
  }, [initialScrollPosition]);

  // 滚动事件监听
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !onScrollPositionChange) return;

    const handleScroll = () => {
      onScrollPositionChange(container.scrollTop);
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [onScrollPositionChange]);

  // 监听跳转事件
  useEffect(() => {
    const handleScrollToPreviewHeading = (event: CustomEvent<{ headingId: string }>) => {
      if (containerRef.current) {
        const headingElement = containerRef.current.querySelector(`#${event.detail.headingId}`);
        if (headingElement) {
          headingElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    };

    document.addEventListener('scrollToPreviewHeading', handleScrollToPreviewHeading as EventListener);
    
    return () => {
      document.removeEventListener('scrollToPreviewHeading', handleScrollToPreviewHeading as EventListener);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className={`prose prose-slate dark:prose-invert max-w-none p-4 overflow-auto h-full ${className}`}
      style={{
        lineHeight: '1.7',
        fontSize: '16px'
      }}
    />
  );
} 