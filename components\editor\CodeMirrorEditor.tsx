'use client';

import React, { useEffect, useRef, useState, useMemo } from 'react';
import { EditorView } from '@codemirror/view';
import { basicSetup } from 'codemirror';
import { EditorState } from '@codemirror/state';
import { markdown } from '@codemirror/lang-markdown';
import { oneDark } from '@codemirror/theme-one-dark';
import { useTheme } from 'next-themes';
<<<<<<< Updated upstream
import { debounce } from 'lodash-es';
=======
import { shouldBypassDebounce, calculateAdaptiveDebounce, shouldImmediateRefresh } from '@/lib/boundary-index';
>>>>>>> Stashed changes

interface CodeMirrorEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  initialScrollPosition?: number;
  onScrollPositionChange?: (position: number) => void;
}

export default function CodeMirrorEditor({
  value,
  onChange,
  placeholder = '开始编写您的 Markdown...',
  className = '',
  initialScrollPosition,
  onScrollPositionChange
}: CodeMirrorEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const viewRef = useRef<EditorView | null>(null);
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);

<<<<<<< Updated upstream
  // M1: 实现120ms防抖机制
  const debouncedOnChange = useMemo(() => {
    return debounce((newValue: string) => {
      // 使用requestAnimationFrame确保在下次重绘前执行
      requestAnimationFrame(() => {
        onChange(newValue);
      });
    }, 120, {
      leading: false,  // 不在开始时执行
      trailing: true   // 在结束时执行
    });
=======
  useEffect(() => {
    setMounted(true);
  }, []);

  // 自适应防抖机制 - 根据文档复杂度动态调整防抖时间
  const debouncedOnChange = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    let lastChangeTime = 0;
    let lastChanges: any = null;

    return (newValue: string, changes?: any, changeInfo?: { lines: number; bytes: number }) => {
      const now = Date.now();
      lastChangeTime = now;
      lastChanges = changes;

      // 检查是否需要绕过防抖（大块变更）
      if (changeInfo && shouldBypassDebounce(changeInfo)) {
        clearTimeout(timeoutId);
        requestAnimationFrame(() => {
          onChange(newValue, lastChanges);
        });
        return;
      }

      // 计算自适应防抖时间
      const totalLines = newValue.split('\n').length;
      const debounceTime = calculateAdaptiveDebounce(totalLines);

      // 正常防抖处理
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        // 检查是否应该立即刷新（输入停止时间足够长）
        const inputIdleTime = Date.now() - lastChangeTime;
        if (inputIdleTime >= debounceTime || shouldImmediateRefresh(inputIdleTime)) {
          requestAnimationFrame(() => {
            onChange(newValue, lastChanges);
          });
        }
      }, debounceTime);
    };
>>>>>>> Stashed changes
  }, [onChange]);

  // 清理防抖函数
  useEffect(() => {
    return () => {
      debouncedOnChange.cancel();
    };
  }, [debouncedOnChange]);

  useEffect(() => {
    setMounted(true);
  }, []);

  // 跳转到指定行的函数
  const scrollToLine = (lineNumber: number) => {
    if (!viewRef.current) return;
    
    const view = viewRef.current;
    const line = Math.max(1, Math.min(lineNumber, view.state.doc.lines));
    const pos = view.state.doc.line(line).from;
    
    // 设置光标位置并滚动到可视区域
    view.dispatch({
      selection: { anchor: pos, head: pos },
      scrollIntoView: true
    });
    
    // 聚焦编辑器
    view.focus();
  };

  // 监听来自大纲面板的跳转事件
  useEffect(() => {
    const handleScrollToLine = (event: CustomEvent<{ line: number }>) => {
      scrollToLine(event.detail.line);
    };

    // 注册事件监听器
    document.addEventListener('scrollToLine', handleScrollToLine as EventListener);
    
    return () => {
      document.removeEventListener('scrollToLine', handleScrollToLine as EventListener);
    };
  }, []);

  useEffect(() => {
    if (!mounted || !editorRef.current) return;

    // 创建编辑器状态
    const state = EditorState.create({
      doc: value,
      extensions: [
        basicSetup,
        markdown(),
        theme === 'dark' ? oneDark : [],
        EditorView.updateListener.of((update) => {
          if (update.docChanged) {
            const newValue = update.state.doc.toString();
            // M1: 实现120ms防抖机制，减少无效渲染
            debouncedOnChange(newValue);
          }
          // 监听滚动变化（不防抖，保持实时性）
          if (update.viewportChanged && onScrollPositionChange) {
            const scrollTop = update.view.scrollDOM.scrollTop;
            onScrollPositionChange(scrollTop);
          }
        }),
        EditorView.theme({
          '&': {
            height: '100%',
            fontSize: '14px',
            fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace'
          },
          '.cm-content': {
            padding: '16px',
            minHeight: '100%',
            lineHeight: '1.6'
          },
          '.cm-focused': {
            outline: 'none'
          },
          '.cm-editor': {
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          },
          '.cm-scroller': {
            flex: '1',
            overflow: 'auto'
          },
          // 行号样式配置
          '.cm-gutters': {
            backgroundColor: theme === 'dark' ? '#21252b' : '#fafafa',
            borderRight: `1px solid ${theme === 'dark' ? '#3c4043' : '#e1e4e8'}`,
            color: theme === 'dark' ? '#6c7086' : '#656d76'
          },
          '.cm-lineNumbers .cm-gutterElement': {
            color: theme === 'dark' ? '#6c7086' : '#656d76',
            fontSize: '13px',
            minWidth: '3em',
            textAlign: 'right',
            paddingRight: '8px',
            paddingLeft: '4px'
          },
          '.cm-activeLineGutter': {
            backgroundColor: theme === 'dark' ? '#2c313c' : '#f6f8fa',
            color: theme === 'dark' ? '#ffffff' : '#24292f'
          },
          '.cm-activeLine': {
            backgroundColor: theme === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)'
          }
        }),
        EditorView.lineWrapping
      ]
    });

    // 创建编辑器视图
    const view = new EditorView({
      state,
      parent: editorRef.current
    });

    viewRef.current = view;

    // 恢复滚动位置
    if (initialScrollPosition !== undefined) {
      setTimeout(() => {
        if (view.scrollDOM) {
          view.scrollDOM.scrollTop = initialScrollPosition;
        }
      }, 100);
    }

    return () => {
      view.destroy();
      viewRef.current = null;
    };
  }, [mounted, theme]);

  // 当外部value变化时更新编辑器内容
  useEffect(() => {
    if (viewRef.current && value !== viewRef.current.state.doc.toString()) {
      const transaction = viewRef.current.state.update({
        changes: {
          from: 0,
          to: viewRef.current.state.doc.length,
          insert: value
        }
      });
      viewRef.current.dispatch(transaction);
    }
  }, [value]);

  if (!mounted) {
    return (
      <div className={`w-full h-full flex items-center justify-center ${className}`}>
        <div className="text-muted-foreground">加载编辑器...</div>
      </div>
    );
  }

  return (
    <div 
      ref={editorRef} 
      className={`w-full h-full ${className}`}
    />
  );
}
